from typing import Literal, Callable
import numpy as np
from datetime import datetime
import math
from scipy.linalg import solve
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

# 模糊理论实现
class FuzzySystem:
    """基于模糊理论的决策系统"""
    def __init__(self):
        self.q = 2  # q-ROFS参数
        # 模糊集定义
        self.stability_sets = {
            "Low": lambda x: max(0, min(1, (0.4 - x) * 5)) if x < 0.4 else 0,
            "Medium": lambda x: max(0, min(1, (x - 0.3) * 5, (0.7 - x) * 5)),
            "High": lambda x: max(0, min(1, (x - 0.6) * 5))
        }

        self.volatility_sets = {
            "Low": lambda x: max(0, min(1, (0.03 - x) * 50)) if x < 0.03 else 0,
            "Medium": lambda x: max(0, min(1, (x - 0.02) * 50, (0.06 - x) * 50)),
            "High": lambda x: max(0, min(1, (x - 0.05) * 20))
        }

        self.profit_sets = {
            "Negative": lambda x: max(0, min(1, (0 - x) * 100)) if x < 0 else 0,
            "Low": lambda x: max(0, min(1, (x - 0.01) * 100, (0.03 - x) * 100)),
            "Medium": lambda x: max(0, min(1, (x - 0.02) * 100, (0.05 - x) * 100)),
            "High": lambda x: max(0, min(1, (x - 0.04) * 50))
        }
        
        # 模糊规则库
        self.rules = [
            # 低稳定性规则
            {"condition": lambda s, v, _p: s["Low"] > 0.7 and v["Low"] > 0.7,
             "action": lambda: ("RiskLow", "Aggressive")},
            {"condition": lambda s, v, _p: s["Low"] > 0.7 and v["Medium"] > 0.7,
             "action": lambda: ("RiskLow", "Conservative")},
            {"condition": lambda s, v, _p: s["Low"] > 0.7 and v["High"] > 0.7,
             "action": lambda: ("RiskNone", "Stop")},

            # 中等稳定性规则
            {"condition": lambda s, v, p: s["Medium"] > 0.7 and v["Low"] > 0.7 and p["Medium"] > 0.7,
             "action": lambda: ("RiskMedium", "Aggressive")},
            {"condition": lambda s, v, p: s["Medium"] > 0.7 and v["Low"] > 0.7 and p["Low"] > 0.7,
             "action": lambda: ("RiskMedium", "Normal")},
            {"condition": lambda s, v, _p: s["Medium"] > 0.7 and v["Medium"] > 0.7,
             "action": lambda: ("RiskLow", "Conservative")},
            {"condition": lambda s, v, _p: s["Medium"] > 0.7 and v["High"] > 0.7,
             "action": lambda: ("RiskNone", "Stop")},

            # 高稳定性规则
            {"condition": lambda s, v, p: s["High"] > 0.7 and v["Low"] > 0.7 and p["High"] > 0.7,
             "action": lambda: ("RiskHigh", "Aggressive")},
            {"condition": lambda s, v, p: s["High"] > 0.7 and v["Low"] > 0.7 and p["Medium"] > 0.7,
             "action": lambda: ("RiskHigh", "Normal")},
            {"condition": lambda s, v, p: s["High"] > 0.7 and v["Low"] > 0.7 and p["Low"] > 0.7,
             "action": lambda: ("RiskMedium", "Normal")},
            {"condition": lambda s, v, _p: s["High"] > 0.7 and v["Medium"] > 0.7,
             "action": lambda: ("RiskMedium", "Conservative")},
            {"condition": lambda s, v, _p: s["High"] > 0.7 and v["High"] > 0.7,
             "action": lambda: ("RiskLow", "Stop")},
        ]
        
        # 去模糊化参数
        self.risk_levels = {
            "RiskNone": 0.0,
            "RiskLow": 0.3,
            "RiskMedium": 0.6,
            "RiskHigh": 0.9
        }
        
        self.action_levels = {
            "Stop": 0.0,
            "Conservative": 0.3,
            "Normal": 0.6,
            "Aggressive": 0.9
        }

    def fuzzify(self, stability, volatility, profit):
        s_membership = {k: f(stability) for k, f in self.stability_sets.items()}
        v_membership = {k: f(volatility) for k, f in self.volatility_sets.items()}
        p_membership = {k: f(profit) for k, f in self.profit_sets.items()}
        return s_membership, v_membership, p_membership

    def infer(self, s_mem, v_mem, p_mem):
        """执行模糊推理"""
        activated_rules = []
        for rule in self.rules:
            if rule["condition"](s_mem, v_mem, p_mem):
                action = rule["action"]()
                activated_rules.append((action, 1.0))  # 简化：所有激活规则权重为1
        
        if not activated_rules:
            return ("RiskMedium", "Normal"), 0.5
        
        # 计算加权平均
        risk_sum = 0.0
        action_sum = 0.0
        total_weight = len(activated_rules)
        
        for (risk, action), weight in activated_rules:
            risk_sum += self.risk_levels[risk] * weight
            action_sum += self.action_levels[action] * weight
        
        avg_risk = risk_sum / total_weight
        avg_action = action_sum / total_weight
        confidence = 0.7  # 简化：固定置信度
        
        return (avg_risk, avg_action), confidence

    def dynamic_weight(self, μ, _ν, π):
        """基于偏好度的动态权重"""
        return (μ**self.q + μ**self.q * π**self.q)**(1/self.q)

    def make_decision(self, stability, volatility, profit):
        """执行模糊决策"""
        s_mem, v_mem, p_mem = self.fuzzify(stability, volatility, profit)
        (risk_value, action_value), confidence = self.infer(s_mem, v_mem, p_mem)

        # 映射回语义值
        risk_level = min(self.risk_levels.keys(),
                         key=lambda k: abs(self.risk_levels[k] - risk_value))
        action_level = min(self.action_levels.keys(),
                           key=lambda k: abs(self.action_levels[k] - action_value))

        return (risk_level, action_level, confidence)

class IntuitiveTrapezoidalFuzzyNumber:
    """
    直觉梯形模糊数（改进型）
    特点：
    - 支持非对称隶属函数
    - 集成犹豫度计算
    - 带重心坐标优化
    """
    def __init__(self, a, b, c, d, mu, nu, a1=None, d1=None):
        self.a = a  # 隶属函数左起点
        self.b = b  # 隶属函数左顶点
        self.c = c  # 隶属函数右顶点
        self.d = d  # 隶属函数右终点
        self.mu = mu  # 隶属度峰值
        self.nu = nu  # 非隶属度谷值
        self.a1 = a1 if a1 is not None else a  # 非隶属函数左起点
        self.d1 = d1 if d1 is not None else d  # 非隶属函数右终点

    def membership(self, x):
        """计算x的隶属度"""
        if x < self.a or x > self.d:
            return 0.0
        elif self.a <= x < self.b:
            return self.mu * (x - self.a) / (self.b - self.a)
        elif self.b <= x <= self.c:
            return self.mu
        else:  # self.c < x <= self.d
            return self.mu * (self.d - x) / (self.d - self.c)

    def non_membership(self, x):
        """计算x的非隶属度"""
        if x < self.a1 or x > self.d1:
            return 1.0
        elif self.a1 <= x < self.b:
            return self.nu + (1 - self.nu) * (x - self.a1) / (self.b - self.a1)
        elif self.b <= x <= self.c:
            return self.nu
        else:  # self.c < x <= self.d1
            return self.nu + (1 - self.nu) * (self.d1 - x) / (self.d1 - self.c)

    def hesitation(self, x):
        """计算x的犹豫度"""
        return 1 - self.membership(x) - self.non_membership(x)

    def centroid_x(self):
        """计算重心横坐标期望值"""
        # 隶属函数区域重心 (详细积分推导见:cite[8])
        x_l = (self.c**2 + self.d**2 - self.a**2 - self.b**2 +
               self.d*self.c - self.a*self.b) / (3*(self.c + self.d - self.b - self.a))
        
        # 非隶属函数区域重心
        x_f = (-self.nu*(self.a1**2 + self.b**2 + self.a1*self.b - self.c**2 - self.d1**2 - self.d1*self.c)
               - 2*self.a1**2 + self.b**2 + self.a1*self.b - self.c**2 + 2*self.d1**2 - self.c*self.d1
               ) / (3*self.nu*(self.c + self.d1 - self.a1 - self.b) + 3*(self.b + self.d1 - self.a1 - self.c))
        
        # 犹豫度区域重心 (简化计算)
        x_d = (self.a + self.b + self.c + self.d) / 4.0
        
        return (x_l + x_f + x_d) / 3.0

class TrapezoidalFuzzyNumber:
    """梯形模糊数实现"""
    def __init__(self, a, b, c, d):
        assert a <= b <= c <= d, "Invalid trapezoid parameters"
        self.a = a
        self.b = b
        self.c = c
        self.d = d
        
    def membership(self, x):
        """计算隶属度"""
        if x < self.a:
            return 0.0
        elif self.a <= x < self.b:
            return (x - self.a) / (self.b - self.a)
        elif self.b <= x <= self.c:
            return 1.0
        elif self.c < x <= self.d:
            return (self.d - x) / (self.d - self.c)
        else:
            return 0.0
    
    def __add__(self, other):
        """梯形模糊数加法"""
        return TrapezoidalFuzzyNumber(
            self.a + other.a,
            self.b + other.b,
            self.c + other.c,
            self.d + other.d
        )
    
    def __mul__(self, other):
        """梯形模糊数乘法"""
        return TrapezoidalFuzzyNumber(
            self.a * other.a,
            self.b * other.b,
            self.c * other.c,
            self.d * other.d
        )
    
    def distance(self, other):
        """计算梯形模糊数距离"""
        return math.sqrt(
            0.25 * ((self.a - other.a)**2 +
                   (self.b - other.b)**2 +
                   (self.c - other.c)**2 +
                   (self.d - other.d)**2)
        )
    
    def centroid(self):
        """计算重心去模糊化值"""
        return (self.a + self.b + self.c + self.d) / 4.0
    
    def __repr__(self):
        return f"TrapezoidalFuzzyNumber({self.a}, {self.b}, {self.c}, {self.d})"

class AdvancedFuzzySystem:
    """集成梯形模糊数的模糊系统"""
    def __init__(self):
        # 梯形模糊集定义
        self.stability_sets = {
            "Low": TrapezoidalFuzzyNumber(0.0, 0.1, 0.3, 0.4),
            "Medium": TrapezoidalFuzzyNumber(0.3, 0.4, 0.6, 0.7),
            "High": TrapezoidalFuzzyNumber(0.6, 0.7, 0.9, 1.0)
        }
        self.volatility_sets = {
            "Low": TrapezoidalFuzzyNumber(0.0, 0.01, 0.03, 0.05),
            "Medium": TrapezoidalFuzzyNumber(0.03, 0.05, 0.07, 0.09),
            "High": TrapezoidalFuzzyNumber(0.07, 0.09, 0.12, 0.15)
        }
        self.profit_sets = {
            "Negative": TrapezoidalFuzzyNumber(-0.1, -0.08, -0.03, 0.0),
            "Low": TrapezoidalFuzzyNumber(-0.02, 0.0, 0.02, 0.04),
            "Medium": TrapezoidalFuzzyNumber(0.02, 0.04, 0.06, 0.08),
            "High": TrapezoidalFuzzyNumber(0.06, 0.08, 0.12, 0.15)
        }
        # 梯形模糊规则库
        self.rules = [
            {"condition": lambda s, v, p: s.get("Low", 0) > 0.7 and v.get("Low", 0) > 0.7,
             "action": lambda: ("RiskLow", "Aggressive", 0.8)},
            {"condition": lambda s, v, p: s.get("Medium", 0) > 0.7 and v.get("Low", 0) > 0.7 and p.get("Medium", 0) > 0.7,
             "action": lambda: ("RiskMedium", "Aggressive", 0.9)},
            {"condition": lambda s, v, p: s.get("High", 0) > 0.7 and v.get("Low", 0) > 0.7 and p.get("High", 0) > 0.7,
             "action": lambda: ("RiskHigh", "Aggressive", 0.95)}
        ]
        # 风险-行动关联矩阵
        self.risk_action_matrix = {
            ("RiskLow", "Aggressive"): TrapezoidalFuzzyNumber(0.7, 0.75, 0.85, 0.9),
            ("RiskMedium", "Normal"): TrapezoidalFuzzyNumber(0.5, 0.6, 0.7, 0.8),
            ("RiskHigh", "Conservative"): TrapezoidalFuzzyNumber(0.3, 0.4, 0.5, 0.6),
            ("RiskNone", "Stop"): TrapezoidalFuzzyNumber(0.0, 0.1, 0.2, 0.3)
        }
        self.risk_levels = {
            "RiskNone": 0.0,
            "RiskLow": 0.3,
            "RiskMedium": 0.6,
            "RiskHigh": 0.9
        }
        self.action_levels = {
            "Stop": 0.0,
            "Conservative": 0.3,
            "Normal": 0.6,
            "Aggressive": 0.9
        }
        self.q = 2

    def fuzzify(self, stability, volatility, profit):
        s_membership = {k: f.membership(stability) for k, f in self.stability_sets.items()}
        v_membership = {k: f.membership(volatility) for k, f in self.volatility_sets.items()}
        p_membership = {k: f.membership(profit) for k, f in self.profit_sets.items()}
        return s_membership, v_membership, p_membership

    def infer(self, s_mem, v_mem, p_mem):
        activated_rules = []
        for rule in self.rules:
            strength = rule["condition"](s_mem, v_mem, p_mem)
            if strength > 0:
                action = rule["action"]()
                activated_rules.append((action, strength))
        if not activated_rules:
            return ("RiskMedium", "Normal", 0.5)
        risk_sum = 0.0
        action_sum = 0.0
        confidence_sum = 0.0
        total_strength = sum(strength for _, strength in activated_rules)
        for (risk, action, conf), strength in activated_rules:
            matrix_value = self.risk_action_matrix.get((risk, action), TrapezoidalFuzzyNumber(0.4, 0.5, 0.6, 0.7)).centroid()
            weighted_value = matrix_value * strength
            risk_sum += self._risk_to_value(risk) * weighted_value
            action_sum += self._action_to_value(action) * weighted_value
            confidence_sum += conf * strength
        risk_value = risk_sum / total_strength
        action_value = action_sum / total_strength
        confidence_value = confidence_sum / total_strength
        risk_level = self._value_to_risk(risk_value)
        action_level = self._value_to_action(action_value)
        return (risk_level, action_level, confidence_value)

    def _risk_to_value(self, risk):
        return self.risk_levels[risk]

    def _action_to_value(self, action):
        return self.action_levels[action]

    def _value_to_risk(self, value):
        if value < 0.15:
            return "RiskNone"
        elif value < 0.45:
            return "RiskLow"
        elif value < 0.75:
            return "RiskMedium"
        else:
            return "RiskHigh"

    def _value_to_action(self, value):
        if value < 0.15:
            return "Stop"
        elif value < 0.45:
            return "Conservative"
        elif value < 0.75:
            return "Normal"
        else:
            return "Aggressive"

    def dynamic_weight(self, μ, _ν, π):
        """基于偏好度的动态权重"""
        return (μ**self.q + μ**self.q * π**self.q)**(1/self.q)

    def q_rofs_correlation(self, μ1, μ2, ν1, ν2, π1, π2):
        """q-ROFS相关性计算"""
        q = self.q
        # 计算q-ROFS相关性系数
        numerator = μ1**q * μ2**q + ν1**q * ν2**q + π1**q * π2**q
        denominator = math.sqrt((μ1**q + ν1**q + π1**q) * (μ2**q + ν2**q + π2**q))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator

class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    tech_type: Literal["STC_HULL"] = Field(default="STC_HULL", title="技术指标")
    trade_direction: Literal["buy", "sell", "auto"] = Field(default="auto", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位") 
    order_volume: int = Field(default=1, title="报单数量")
    trail_profit_start: float = Field(default=0.03, title="追踪止盈启动(3%)")
    trail_profit_stop: float = Field(default=0.01, title="追踪回撤平仓(1%)")
    quick_stop_loss: float = Field(default=0.02, title="快速止损(2%)")
    volatility_threshold: float = Field(default=0.05, title="波动率阈值")
    stability_margin: float = Field(default=0.7, title="稳定裕度")
    N1: int = Field(default=9, title="HULL周期参数1")
    P1: int = Field(default=9, title="HULL周期参数2")

class State(BaseState):
    """状态映射模型"""
    stc_value: float = Field(default=0, title="STC值")
    stc_signal: float = Field(default=0, title="STC信号线")
    hull_value: float = Field(default=0, title="HULL值")
    hull_prev: float = Field(default=0, title="HULL前值")
    position_cost: float = Field(default=0.0, title="持仓成本价")
    max_profit: float = Field(default=0.0, title="最高盈利比例")
    stop_triggered: bool = Field(default=False, title="止损触发标志")
    system_stability: float = Field(default=1.0, title="系统稳定指数")
    filtered_price: float = Field(default=0.0, title="滤波后价格")
    volatility_index: float = Field(default=0.0, title="波动率指数")
    lyapunov_value: float = Field(default=0.0, title="李雅普诺夫值")
    fuzzy_risk: str = Field(default="Medium", title="模糊风险等级")
    fuzzy_action: str = Field(default="Normal", title="模糊行动级别")
    fuzzy_confidence: float = Field(default=0.5, title="模糊决策置信度")
    ma1: float = Field(default=0.0, title="HULL移动平均1")
    ma0: float = Field(default=0.0, title="HULL移动平均0")
    bup: float = Field(default=0.0, title="HULL上轨")
    bdn: float = Field(default=0.0, title="HULL下轨")
    fast_line: float = Field(default=0.0, title="STC快线")
    slow_line: float = Field(default=0.0, title="STC慢线")
    cycle_length: int = Field(default=10, title="STC周期长度")

class Strategy3(BaseStrategy):
    """梯形模糊数增强版交易策略"""
    
    def execute_fuzzy_decision(self):
        """执行梯形模糊决策"""
        # 计算当前盈利比例
        current_profit = 0.0
        if self.state_map.position_cost > 0 and self.state_map.filtered_price > 0:
            current_profit = (self.state_map.filtered_price - self.state_map.position_cost) / self.state_map.position_cost
        # 执行梯形模糊决策
        decision = self.control_center.trapezoidal_fuzzy_decision(
            self.state_map.system_stability,
            self.state_map.volatility_index,
            current_profit
        )
        # 应用决策到状态
        if isinstance(decision, tuple) and len(decision) >= 3:
            self.state_map.fuzzy_risk = str(decision[0])
            self.state_map.fuzzy_action = str(decision[1])
            self.state_map.fuzzy_confidence = float(decision[2])
        # 每周执行自适应规则更新
        if datetime.now().weekday() == 0:  # 每周一
            self.control_center.adaptive_rule_update()
        # 更新状态栏
        self.update_status_bar()



    def execute_close_position(self, position, direction):
        """执行平仓操作"""
        if self.tick:
            price = self.tick.bid_price1 if direction == "sell" else self.tick.ask_price1
            self.signal_price = -price if direction == "sell" else price
            self.order_id = self.auto_close_position(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                price=price,
                volume=abs(position.net_position),
                order_direction=direction
            )

    def execute_open_position(self, direction, volume):
        """执行开仓操作"""
        if self.tick:
            price = self.tick.ask_price1 if direction == "buy" else self.tick.bid_price1
            self.signal_price = price if direction == "buy" else -price
            self.order_id = self.send_order(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                volume=volume,
                price=price,
                order_direction=direction
            )

    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        self.control_center = ControlCenter(self)
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False
        # 市场数据
        self.tick: TickData = None
        self.price_history = []
        # 技术指标
        self.ma00 = 0
        self.ma10 = 0
        self.kk = 0
        self.dd = 0
        self.jj = 0
        self.tr = 0
        self.macd1 = 0
        self.signall1 = 0
        # 订单管理
        self.order_id = None
        self.signal_price = 0
        # 系统状态
        self.last_update = datetime.now()
        self.stability_counter = 0
        self.last_profit = 0.0

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """HULL主图指标"""
        return {
            f"HULL{self.params_map.N1}": self.state_map.ma1,
            f"HULL{self.params_map.P1}": self.state_map.ma0,
            "HULL_UPPER": self.state_map.bup,
            "HULL_LOWER": self.state_map.bdn
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """STC指标数据输出（Schaff趋势周期）"""
        return {
            "STC_FAST": self.state_map.fast_line,
            "STC_SLOW": self.state_map.slow_line,
            "STC_CYCLE": self.state_map.cycle_length
        }

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)
        
        # 更新滤波后价格
        mid_price = (tick.ask_price1 + tick.bid_price1) / 2
        self.price_history.append(mid_price)
        if len(self.price_history) > 100:
            self.price_history.pop(0)
        
        # 应用卡尔曼滤波
        filtered_price = self.control_center.kalman_filter(mid_price)
        self.state_map.filtered_price = filtered_price
        
        # 计算波动率
        if len(self.price_history) > 10:
            returns = np.diff(np.log(self.price_history[-10:]))
            volatility = np.std(returns) * math.sqrt(252)  # 年化波动率
            self.state_map.volatility_index = float(volatility)
        
        # 每分钟执行模糊决策
        current_time = datetime.now()
        if (current_time - self.last_update).seconds > 60:
            self.execute_fuzzy_decision()
            self.last_update = current_time

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        super().on_trade(trade, log)
        self.order_id = None
        self.stability_counter += 1
        
        # 记录交易利润
        if trade.offset == "close" and self.state_map.position_cost > 0:
            self.last_profit = (trade.price - self.state_map.position_cost) / self.state_map.position_cost
        
        # 每5次交易检查系统稳定性
        if self.stability_counter >= 5:
            self.check_system_stability()
            self.stability_counter = 0

    def on_start(self):
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        super().on_start()
        
        # 初始化状态
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None
        self.price_history = []
        self.stability_counter = 0
        self.last_profit = 0.0
        self.update_status_bar()

    def on_stop(self):
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        # 计算指标
        self.calc_indicator()
        
        # 计算信号
        self.calc_signal(kline)
        
        # 信号执行
        self.exec_signal()
        
        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self.calc_indicator()
        
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        self.update_status_bar()

    def check_system_stability(self):
        """综合稳定性检查"""
        # 定义系统传递函数
        def transfer_func(s):
            return (self.kline_generator.producer.ema(10)[-1] * s + 0.5) / (s**2 + 0.6*s + 0.1)
        
        # 执行稳定性检查
        is_stable = self.control_center.system_stability_check(transfer_func)
        stability_level = 1.0 if is_stable else 0.6
        
        # 考虑波动率因素
        volatility_factor = 1.0 - min(1.0, self.state_map.volatility_index / self.params_map.volatility_threshold)
        self.state_map.system_stability = stability_level * volatility_factor
        
        if self.state_map.system_stability < self.params_map.stability_margin:
            self.write_log(f"系统稳定性低: {self.state_map.system_stability:.2f} < {self.params_map.stability_margin}")

    def calc_stc_hull_signal(self):
        """增强信号生成"""
        # 获取模糊决策结果
        decision = self.control_center.fuzzy_decision(
            self.state_map.system_stability,
            self.state_map.volatility_index,
            self.last_profit
        )
        
        # q-ROFS相关性过滤
        corr = self.control_center.advanced_fuzzy_system.q_rofs_correlation(
            μ1=self.state_map.stc_value,
            μ2=self.state_map.hull_value,
            ν1=1-self.state_map.stc_value,
            ν2=1-self.state_map.hull_value,
            π1=0.2, π2=0.2
        )
        
        # 获取STC和Hull指标状态
        stc_bull = self.state_map.stc_value > self.state_map.stc_signal
        hull_bull = self.state_map.hull_value > self.state_map.hull_prev
        
        # 综合判断
        if decision[0] == "High" and corr > 0.8:
            self.buy_signal = stc_bull and hull_bull
            self.short_signal = not stc_bull and not hull_bull
        
        # 1. 检查止盈止损条件
        current_price = self.state_map.filtered_price
        if self.state_map.position_cost > 0:
            profit_ratio = (current_price - self.state_map.position_cost) / self.state_map.position_cost
            
            # 应用PID控制器动态调整止损参数
            error = profit_ratio - self.params_map.trail_profit_start
            adjusted_stop = self.control_center.update_pid(error)
            
            # 追踪止盈逻辑
            if profit_ratio >= self.params_map.trail_profit_start:
                self.state_map.max_profit = max(self.state_map.max_profit, profit_ratio)
                if profit_ratio < (self.state_map.max_profit - max(self.params_map.trail_profit_stop, adjusted_stop)):
                    self.cover_signal = True
                    self.sell_signal = True
                    self.state_map.stop_triggered = True
                    return
            
            # 快速止损逻辑
            if profit_ratio <= -self.params_map.quick_stop_loss:
                self.cover_signal = True
                self.sell_signal = True
                self.state_map.stop_triggered = True
                return

        # 2. 原始指标信号生成
        stc_bull = self.state_map.stc_value > self.state_map.stc_signal
        hull_bull = self.state_map.hull_value > self.state_map.hull_prev
        
        # 3. 综合信号处理 (考虑模糊决策)
        if self.state_map.fuzzy_action == "Stop":
            # 停止交易信号
            self.buy_signal = False
            self.short_signal = False
            return
            
        stability_factor = max(0.5, self.state_map.system_stability)
        
        if self.params_map.trade_direction == "auto":
            self.buy_signal = stc_bull and hull_bull and not self.state_map.stop_triggered
            self.short_signal = (not stc_bull and not hull_bull) and not self.state_map.stop_triggered
            self.cover_signal = self.buy_signal
            self.sell_signal = self.short_signal
            
            # 应用模糊行动级别
            if self.state_map.fuzzy_action == "Conservative":
                self.buy_signal = self.buy_signal and stability_factor > 0.8
                self.short_signal = self.short_signal and stability_factor > 0.8
            elif self.state_map.fuzzy_action == "Aggressive":
                self.buy_signal = self.buy_signal and stability_factor > 0.6
                self.short_signal = self.short_signal and stability_factor > 0.6
        else:
            self.buy_signal = stc_bull and hull_bull and not self.state_map.stop_triggered
            self.short_signal = (not stc_bull and not hull_bull) and not self.state_map.stop_triggered
            
            if self.params_map.trade_direction == "buy":
                self.buy_signal, self.short_signal = self.short_signal, self.buy_signal
            
            self.cover_signal = self.buy_signal
            self.sell_signal = self.short_signal

    def calc_indicator(self) -> None:
        """计算STC和HULL指标 (使用滤波后价格)"""
        # 使用卡尔曼滤波后的价格计算指标
        prices = self.kline_generator.producer.close_array
        if len(prices) < 30:
            return
        
        # 计算STC指标 (Schaff Trend Cycle)
        fast_period = 5    # 快速EMA周期
        slow_period = 20   # 慢速EMA周期
        cycle_period = 10  # STC周期
        signal_period = 3  # 信号线周期
        
        # 使用滤波后价格计算MACD线
        fast_ema = self.kline_generator.producer.ema(fast_period, array=True)
        slow_ema = self.kline_generator.producer.ema(slow_period, array=True)
        macd_line = fast_ema - slow_ema
        
        # 计算STC值
        lowest = np.minimum.accumulate(macd_line[-cycle_period:])
        highest = np.maximum.accumulate(macd_line[-cycle_period:])
        stoch = 100 * (macd_line[-1] - lowest[-1]) / (highest[-1] - lowest[-1] + 1e-9)
        
        # 平滑处理
        stc_value = self.kline_generator.producer.ema(signal_period, array=True, prices=stoch)[-1]
        stc_signal = self.kline_generator.producer.ema(signal_period, array=True, prices=stc_value)[-1]
        
        self.state_map.stc_value, self.state_map.stc_signal = np.round((stc_value, stc_signal), 2)

        # 计算HULL移动平均线 (Hull Moving Average)
        hull_period = 9  # HULL周期参数
        
        # 计算WMA(period/2)和WMA(period)
        half_period = hull_period // 2
        wma_half = self.kline_generator.producer.wma(half_period, array=True)
        wma_full = self.kline_generator.producer.wma(hull_period, array=True)
        
        # 计算原始HULL序列
        raw_hull = 2 * wma_half - wma_full
        
        # 计算最终HULL值 (WMA(sqrt(period)))
        sqrt_period = int(np.sqrt(hull_period))
        hull_ma = self.kline_generator.producer.wma(sqrt_period, array=True, prices=raw_hull)
        
        self.state_map.hull_prev, self.state_map.hull_value = np.round((hull_ma[-2], hull_ma[-1]), 2)
        
        # 李雅普诺夫稳定性分析
        state_vector = np.array([self.state_map.stc_value, self.state_map.hull_value])
        A_matrix = np.array([[0.9, 0.1], [0.05, 0.85]])  # 状态转移矩阵
        lyapunov_value = self.control_center.lyapunov_stability(state_vector, A_matrix)
        self.state_map.lyapunov_value = lyapunov_value

    def calc_signal(self, kline: KLineData):
        """计算交易信号"""
        self.calc_stc_hull_signal()
        self.long_price = self.short_price = kline.close
        
        if self.tick:
            # 使用滤波后价格作为交易参考
            self.long_price = max(self.state_map.filtered_price, self.tick.ask_price1)
            self.short_price = min(self.state_map.filtered_price, self.tick.bid_price1)
            
            if self.params_map.price_type == "D2":
                self.long_price = max(self.state_map.filtered_price, self.tick.ask_price2)
                self.short_price = min(self.state_map.filtered_price, self.tick.bid_price2)

    def on_order(self, order: OrderData):
        """订单回调(更新止盈止损状态)"""
        if order.status == "filled":
            if order.offset == "open":
                # 开仓时记录成本价
                self.state_map.position_cost = order.traded * (1.0003 if order.direction == "long" else 0.9997)
                self.state_map.max_profit = 0.0
                self.state_map.stop_triggered = False
                self.write_log(f"开仓成功: {order.direction} {order.volume}手 @ {order.traded:.2f}")
            else:
                # 平仓时重置状态
                self.state_map.position_cost = 0.0
                self.state_map.max_profit = 0.0
                self.state_map.stop_triggered = False
                self.write_log(f"平仓成功: {order.direction} {order.volume}手 @ {order.traded:.2f}")

    def exec_signal(self):
        """交易信号执行 (考虑模糊决策)"""
        self.signal_price = 0
        position = self.get_position(self.params_map.instrument_id)
        
        # 根据模糊风险等级调整交易量
        risk_factor = {
            "RiskNone": 0.0,
            "RiskLow": 0.3,
            "RiskMedium": 0.6,
            "RiskHigh": 0.9
        }[self.state_map.fuzzy_risk]
        
        adjusted_volume = max(1, int(self.params_map.order_volume * risk_factor))
        
        if self.order_id is not None:
            self.cancel_order(self.order_id)
        
        # 如果风险等级为None，停止所有交易
        if self.state_map.fuzzy_risk == "RiskNone":
            return
        
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
        elif position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
        
        # 开仓信号
        if self.short_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=adjusted_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
        elif self.buy_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=adjusted_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
        
        # 更新状态栏
        self.update_status_bar()

    def update_status_bar(self):
        """更新状态栏显示梯形模糊决策信息"""
        # 获取梯形模糊决策详情
        risk_symbol = {
            "RiskNone": "🚫",
            "RiskLow": "🟢", 
            "RiskMedium": "🟡",
            "RiskHigh": "🔴"
        }[self.state_map.fuzzy_risk]
        
        action_symbol = {
            "Stop": "⏹️",
            "Conservative": "🚸",
            "Normal": "➡️",
            "Aggressive": "⚡"
        }[self.state_map.fuzzy_action]
        
        status_text = (
            f"STC: {self.state_map.stc_value:.2f}/{self.state_map.stc_signal:.2f} | "
            f"HULL: {self.state_map.hull_value:.2f} | "
            f"稳定: {self.state_map.system_stability:.2f} | "
            f"波动: {self.state_map.volatility_index:.4f} | "
            f"模糊决策: {risk_symbol}{action_symbol} {self.state_map.fuzzy_confidence:.0%}"
        )
        self.widget.update_status(status_text)

# 控制理论相关函数
def mason_formula(adjacency_matrix: np.ndarray) -> float:
    """梅森公式计算系统稳定性"""
    n = adjacency_matrix.shape[0]
    identity = np.eye(n)
    det = np.linalg.det(identity - adjacency_matrix)
    return det

def routh_stability(coeffs: np.ndarray) -> bool:
    """劳斯判据判断系统稳定性"""
    n = len(coeffs)
    if n < 2:
        return False
    
    routh_table = []
    routh_table.append(coeffs[0::2])
    routh_table.append(coeffs[1::2] + [0] if n % 2 == 1 else coeffs[1::2])
    
    for i in range(2, n):
        row = []
        for j in range(len(routh_table[i-1]) - 1):
            a = routh_table[i-2][j+1]
            b = routh_table[i-2][0]
            c = routh_table[i-1][j+1]
            d = routh_table[i-1][0]
            if d == 0:  # 避免除以零
                d = 1e-10
            element = (a * d - b * c) / d
            row.append(element)
        routh_table.append(row)
        
        # 检查第一列符号变化
        if row and row[0] * routh_table[i-1][0] < 0:
            return False
    
    return True

def nyquist_criterion(transfer_function: Callable, omega_range: tuple) -> bool:
    """奈奎斯特稳定判据"""
    omega = np.linspace(omega_range[0], omega_range[1], 1000)
    response = transfer_function(1j * omega)
    
    real_part = np.real(response)
    imag_part = np.imag(response)
    
    # 计算包围(-1,0)点的次数
    crossings = 0
    for i in range(len(real_part) - 1):
        if imag_part[i] < 0 and imag_part[i+1] >= 0 and real_part[i] < -1:
            crossings += 1
        elif imag_part[i] >= 0 and imag_part[i+1] < 0 and real_part[i] < -1:
            crossings -= 1
    
    return crossings == 0

class ControlCenter:
    """高级控制理论集成中心"""
    def __init__(self, strategy):
        self.strategy = strategy
        self.pid_history = []
        self.kalman_state = None
        self.lyapunov_history = []
        self.fuzzy_system = FuzzySystem()
        self.advanced_fuzzy_system = AdvancedFuzzySystem()
        self.decision_history = []
        
        # PID控制器参数
        self.kp = 0.5  # 比例系数
        self.ki = 0.1  # 积分系数
        self.kd = 0.2  # 微分系数
        
        # 卡尔曼滤波器参数
        self.kalman_Q = np.eye(2) * 0.01  # 过程噪声协方差
        self.kalman_R = np.eye(1) * 0.1   # 观测噪声协方差
        self.kalman_P = np.eye(2)         # 估计误差协方差
        
        # 模糊决策状态
        self.last_decision = ("RiskMedium", "Normal", 0.5)
        self.decision_counter = 0

    def trapezoidal_fuzzy_decision(self, stability, volatility, profit):
        """梯形模糊决策"""
        # 模糊化输入
        s_mem, v_mem, p_mem = self.advanced_fuzzy_system.fuzzify(stability, volatility, profit)

        # 执行推理
        decision = self.advanced_fuzzy_system.infer(s_mem, v_mem, p_mem)

        # 记录决策历史
        self.decision_history.append((stability, volatility, profit, decision))
        if len(self.decision_history) > 100:
            self.decision_history.pop(0)

        return decision
    
    def adaptive_rule_update(self):
        """自适应规则更新"""
        if len(self.decision_history) < 20:
            return
        
        # 分析决策效果
        success_rate = self.calculate_decision_success()
        
        # 根据成功率调整规则权重
        if success_rate < 0.6:
            self.adjust_rule_weights(0.8)  # 降低权重
        elif success_rate > 0.8:
            self.adjust_rule_weights(1.2)  # 提高权重
    
    def calculate_decision_success(self):
        """计算决策成功率"""
        successes = 0
        for i in range(len(self.decision_history) - 1):
            _, _, _, decision = self.decision_history[i]
            next_profit = self.decision_history[i+1][2]
            
            # 根据决策和后续利润判断是否成功
            if decision[1] == "Aggressive" and next_profit > 0.02:
                successes += 1
            elif decision[1] == "Conservative" and next_profit > -0.01:
                successes += 1
            elif decision[1] == "Stop" and next_profit > -0.03:
                successes += 1
        
        return successes / (len(self.decision_history) - 1)
    
    def adjust_rule_weights(self, factor):
        """调整规则权重"""
        # 在实际实现中，这里会调整规则的激活阈值或权重
        # 简化实现：记录调整日志
        self.strategy.write_log(f"调整模糊规则权重: 因子={factor}")

    def update_pid(self, error: float) -> float:
        """多变量PID控制器"""
        if len(self.pid_history) < 2:
            self.pid_history.append(error)
            return error * self.kp
        
        integral = sum(self.pid_history)
        derivative = error - self.pid_history[-1]
        
        # 应用劳斯判据确保稳定性
        coeffs = np.array([self.kd, self.kp + self.kd, self.ki])
        if not routh_stability(coeffs):
            # 如果不稳定，调整参数
            self.kp *= 0.9
            self.ki *= 0.8
            self.kd *= 0.7
        
        output = (self.kp * error + 
                 self.ki * integral + 
                 self.kd * derivative)
        
        self.pid_history.append(error)
        if len(self.pid_history) > 100:
            self.pid_history.pop(0)
        
        return output
    
    def kalman_filter(self, measurement: float) -> float:
        """卡尔曼滤波状态评估"""
        if self.kalman_state is None:
            self.kalman_state = np.array([[measurement], [0.0]])  # [位置, 速度]
        
        # 预测步骤
        F = np.array([[1, 1], [0, 1]])  # 状态转移矩阵
        self.kalman_state = F @ self.kalman_state
        self.kalman_P = F @ self.kalman_P @ F.T + self.kalman_Q
        
        # 更新步骤
        H = np.array([[1, 0]])  # 观测矩阵
        y = measurement - H @ self.kalman_state
        S = H @ self.kalman_P @ H.T + self.kalman_R
        K = self.kalman_P @ H.T @ np.linalg.inv(S)
        
        self.kalman_state = self.kalman_state + K @ y
        self.kalman_P = (np.eye(2) - K @ H) @ self.kalman_P
        
        return float(self.kalman_state[0][0])
    
    def lyapunov_stability(self, state: np.ndarray, A: np.ndarray) -> float:
        """李雅普诺夫稳定性分析"""
        # 解李雅普诺夫方程: A'P + PA = -I
        n = A.shape[0]
        P = solve(A.T, -np.eye(n))
        P = (P + P.T) / 2  # 确保对称
        
        # 计算李雅普诺夫函数 V(x) = x'Px
        V = state.T @ P @ state
        self.lyapunov_history.append(float(V))
        
        if len(self.lyapunov_history) > 100:
            self.lyapunov_history.pop(0)
        
        # 检查稳定性 (V应为正定且递减)
        if len(self.lyapunov_history) > 5 and self.lyapunov_history[-1] > self.lyapunov_history[-5]:
            self.strategy.write_log("警告: 系统稳定性降低!")
        
        return float(V)
    
    def system_stability_check(self, transfer_function: Callable) -> bool:
        """综合稳定性检查"""
        # 梅森公式判据
        adj_matrix = np.array([[0.2, 0.3, 0.1],
                               [0.1, 0.4, 0.2],
                               [0.3, 0.1, 0.2]])
        mason_stable = abs(mason_formula(adj_matrix)) > 0.5
        
        # 奈奎斯特判据
        nyquist_stable = nyquist_criterion(transfer_function, (0.1, 100))
        
        return mason_stable and nyquist_stable
    
    def fuzzy_decision(self, stability, volatility, profit):
        """执行模糊决策并更新策略参数"""
        self.decision_counter += 1
        
        # 每分钟更新一次决策
        if self.decision_counter >= 60:
            self.last_decision = self.fuzzy_system.make_decision(stability, volatility, profit)
            self.decision_counter = 0
            self.strategy.write_log(f"模糊决策: 风险={self.last_decision[0]}, 行动={self.last_decision[1]}, 置信度={self.last_decision[2]:.2f}")
        
        return self.last_decision
    
    def apply_fuzzy_decision(self, decision):
        """应用模糊决策到策略参数"""
        risk_level, action_level, _ = decision
        
        # 根据风险等级调整交易量
        risk_factor = {
            "RiskNone": 0.0,
            "RiskLow": 0.3,
            "RiskMedium": 0.6,
            "RiskHigh": 0.9
        }[risk_level]
        
        # 根据行动级别调整策略行为
        if action_level == "Stop":
            self.strategy.params_map.order_volume = 0
        elif action_level == "Conservative":
            self.strategy.params_map.trail_profit_start = 0.02
            self.strategy.params_map.quick_stop_loss = 0.015
        elif action_level == "Normal":
            self.strategy.params_map.trail_profit_start = 0.03
            self.strategy.params_map.quick_stop_loss = 0.02
        elif action_level == "Aggressive":
            self.strategy.params_map.trail_profit_start = 0.04
            self.strategy.params_map.quick_stop_loss = 0.025
        
        return risk_factor